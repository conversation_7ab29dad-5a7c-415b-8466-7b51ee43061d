import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentArrowDownIcon,
  PencilIcon,
  TrashIcon,
  DocumentIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronUpDownIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-toastify'
import ConfirmDialog from '../components/ConfirmDialog'
import ColumnVisibilityControl, { useColumnVisibility, ColumnConfig } from '../components/ColumnVisibilityControl'
import Pagination from '../components/Pagination'
import { PermissionGuard } from '../components/PermissionGuard'
import { usePermissions } from '../hooks/usePermissions'

interface Book {
  id: string
  accessionNo: string
  title: string
  titleNepali?: string
  author: {
    id: string
    name: string
    nameNepali?: string
  }
  category: {
    id: string
    name: string
    nameNepali?: string
  }
  language: {
    id: string
    name: string
    nameNepali?: string
    code?: string
  }
  publisher?: string // Legacy field
  publisher_rel?: {
    id: string
    name: string
    nameNepali?: string
    country?: string
  }
  subject?: {
    id: string
    name: string
    nameNepali?: string
  }
  series?: {
    id: string
    name: string
    nameNepali?: string
  }
  location?: {
    id: string
    shelf: string
    row?: string
  }
  condition?: {
    id: string
    name: string
    description: string
    color: string
  }
  publishedYear: number
  pages?: number
  price?: number
  originalPrice?: number
  originalCurrency?: string
  bookNo?: string
  isAvailable: boolean
  shelf?: string
  row?: string
  borrowings: Array<{
    id: string
    member: {
      name: string
      memberNo: string
    }
  }>
  copyInfo?: {
    totalCopies: number
    availableCopies: number
    borrowedCopies: number
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  pages: number
}

// Define default columns for books table
const defaultBookColumns: ColumnConfig[] = [
  { key: 'accessionNo', label: 'Accession No.', visible: true, required: true },
  { key: 'title', label: 'Title', visible: true, required: true },
  { key: 'author', label: 'Author', visible: true },
  { key: 'category', label: 'Category', visible: true },
  { key: 'language', label: 'Language', visible: false },
  { key: 'publisher', label: 'Publisher', visible: true },
  { key: 'year', label: 'Year', visible: true },
  { key: 'copies', label: 'Copies', visible: true },
  { key: 'price', label: 'Price', visible: false },
  { key: 'status', label: 'Status', visible: true },
  { key: 'location', label: 'Location', visible: false },
  { key: 'actions', label: 'Actions', visible: true, required: true }
]

export default function Books() {
  const navigate = useNavigate()
  const permissions = usePermissions()
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [search, setSearch] = useState('')
  const [filters, setFilters] = useState({
    category: '',
    author: '',
    language: '',
    available: ''
  })
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [authors, setAuthors] = useState<Array<{id: string, name: string, nameNepali?: string}>>([])
  const [categories, setCategories] = useState<Array<{id: string, name: string, nameNepali?: string}>>([])
  const [languages, setLanguages] = useState<Array<{id: string, name: string, nameNepali?: string}>>([])
  const [loadingFilters, setLoadingFilters] = useState(false)
  const [deleteDialog, setDeleteDialog] = useState<{isOpen: boolean, bookId: string, bookTitle: string}>({
    isOpen: false,
    bookId: '',
    bookTitle: ''
  })

  // Column visibility management
  const {
    columns: bookColumns,
    toggleColumn,
    resetToDefault,
    isColumnVisible
  } = useColumnVisibility(defaultBookColumns, 'books-table-columns')

  useEffect(() => {
    fetchBooks()
    fetchFiltersData()
  }, [pagination.page, search, filters, sortBy, sortOrder])

  const fetchFiltersData = async () => {
    if (authors.length === 0 || categories.length === 0 || languages.length === 0) {
      setLoadingFilters(true)
      try {
        const [authorsResponse, categoriesResponse, languagesResponse] = await Promise.all([
          fetch('/api/authors'),
          fetch('/api/categories'),
          fetch('/api/languages/active')
        ])

        if (authorsResponse.ok) {
          const authorsData = await authorsResponse.json()
          setAuthors(authorsData)
        }

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          setCategories(categoriesData)
        }

        if (languagesResponse.ok) {
          const languagesData = await languagesResponse.json()
          setLanguages(languagesData)
        }
      } catch (error) {
        console.error('Error fetching filter data:', error)
      } finally {
        setLoadingFilters(false)
      }
    }
  }

  const fetchBooks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search,
        sortBy,
        sortOrder,
        ...filters
      })

      const response = await fetch(`/api/books?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBooks(data.books)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching books:', error)
      toast.error('Failed to fetch books')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteClick = (bookId: string, bookTitle: string) => {
    setDeleteDialog({
      isOpen: true,
      bookId,
      bookTitle
    })
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/books/${deleteDialog.bookId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Book deleted successfully')
        fetchBooks()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete book')
      }
    } catch (error) {
      console.error('Error deleting book:', error)
      toast.error('Failed to delete book')
    }
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch)
    // Reset to page 1 when search changes
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
    // Reset to page 1 when filters change
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      // Toggle sort order if same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new column and default to ascending
      setSortBy(column)
      setSortOrder('asc')
    }
    // Reset to page 1 when sorting changes
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const getSortIcon = (column: string) => {
    if (sortBy !== column) {
      return <ChevronUpDownIcon className="w-4 h-4 text-gray-400" />
    }
    return sortOrder === 'asc' ?
      <ChevronUpIcon className="w-4 h-4 text-primary-600" /> :
      <ChevronDownIcon className="w-4 h-4 text-primary-600" />
  }

  const resetFilters = () => {
    setFilters({
      category: '',
      author: '',
      language: '',
      available: ''
    })
    setSearch('')
    setSortBy('createdAt')
    setSortOrder('desc')
    // Reset to page 1 when clearing filters
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleRowClick = (bookId: string) => {
    navigate(`/books/${bookId}`)
  }

  const handleDownloadPDF = async (book: Book, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent row click

    try {
      toast.info('Generating PDF...', { autoClose: 2000 })

      const response = await fetch(`/api/pdf/book/${book.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate PDF')
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename
      const sanitizedTitle = book.title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '-').toLowerCase()
      link.download = `book-${book.accessionNo}-${sanitizedTitle}.pdf`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up
      window.URL.revokeObjectURL(url)

      toast.success('PDF downloaded successfully!')

    } catch (error) {
      console.error('Error downloading PDF:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to download PDF')
    }
  }

  const handleDownloadAllBooksPDF = async () => {
    try {
      toast.info('Generating all books PDF... This may take a moment.', { autoClose: 3000 })

      const response = await fetch('/api/pdf/books/all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate all books PDF')
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename with current date
      const now = new Date()
      const timestamp = now.toISOString().split('T')[0] // YYYY-MM-DD format
      link.download = `all-books-${timestamp}.pdf`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up
      window.URL.revokeObjectURL(url)

      toast.success('All books PDF downloaded successfully!')

    } catch (error) {
      console.error('Error downloading all books PDF:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to download all books PDF')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Books</h1>
          <p className="text-gray-600">Manage your library's book collection</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <ColumnVisibilityControl
            columns={bookColumns}
            onColumnToggle={toggleColumn}
            onResetToDefault={resetToDefault}
          />
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn btn-secondary btn-md"
          >
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filters
          </button>
          <button
            onClick={handleDownloadAllBooksPDF}
            className="btn btn-secondary btn-md"
            title="Download all books as PDF"
          >
            <DocumentIcon className="w-4 h-4 mr-2" />
            Download All PDF
          </button>
          <PermissionGuard permission="canImportData">
            <Link to="/books/import" className="btn btn-secondary btn-md">
              <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
              Import Books
            </Link>
          </PermissionGuard>
          <PermissionGuard permission="canManageBooks">
            <Link to="/books/new" className="btn btn-primary btn-md">
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Book
            </Link>
          </PermissionGuard>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search books by title, author, accession number..."
                  className="input pl-10"
                  value={search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
              </div>
            </div>
            {showFilters && (
              <div className="flex flex-wrap gap-3">
                <select
                  className="input w-auto"
                  value={filters.available}
                  onChange={(e) => handleFilterChange({ ...filters, available: e.target.value })}
                >
                  <option value="">All Books</option>
                  <option value="true">Available</option>
                  <option value="false">Borrowed</option>
                </select>
                <select
                  className="input w-auto"
                  value={filters.author}
                  onChange={(e) => handleFilterChange({ ...filters, author: e.target.value })}
                  disabled={loadingFilters}
                >
                  <option value="">All Authors</option>
                  {authors.map((author) => (
                    <option key={author.id} value={author.id}>
                      {author.name} {author.nameNepali && `(${author.nameNepali})`}
                    </option>
                  ))}
                </select>
                <select
                  className="input w-auto"
                  value={filters.category}
                  onChange={(e) => handleFilterChange({ ...filters, category: e.target.value })}
                  disabled={loadingFilters}
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name} {category.nameNepali && `(${category.nameNepali})`}
                    </option>
                  ))}
                </select>
                <select
                  className="input w-auto"
                  value={filters.language}
                  onChange={(e) => handleFilterChange({ ...filters, language: e.target.value })}
                  disabled={loadingFilters}
                >
                  <option value="">All Languages</option>
                  {languages.map((language) => (
                    <option key={language.id} value={language.id}>
                      {language.name} {language.nameNepali && `(${language.nameNepali})`}
                    </option>
                  ))}
                </select>
                <button
                  onClick={resetFilters}
                  className="btn btn-secondary btn-sm"
                >
                  Reset
                </button>
              </div>
            )}
          </div>

          {/* Current Sort Indicator */}
          {(sortBy !== 'createdAt' || sortOrder !== 'desc') && (
            <div className="flex items-center justify-between pt-3 border-t border-gray-200">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>Sorted by:</span>
                <span className="font-medium">
                  {sortBy === 'accessionNo' && 'Accession Number'}
                  {sortBy === 'title' && 'Title'}
                  {sortBy === 'author' && 'Author'}
                  {sortBy === 'category' && 'Category'}
                  {sortBy === 'language' && 'Language'}
                  {sortBy === 'publisher' && 'Publisher'}
                  {sortBy === 'publishedYear' && 'Year'}
                  {sortBy === 'price' && 'Price'}
                  {sortBy === 'createdAt' && 'Date Added'}
                </span>
                <span className="text-primary-600">
                  ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
                </span>
              </div>
              <button
                onClick={() => {
                  setSortBy('createdAt')
                  setSortOrder('desc')
                }}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Reset to default
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Books Table */}
      <div className="card">
        <div className="card-body p-0">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="spinner spinner-lg" />
            </div>
          ) : books.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table-modern">
                <thead className="table-header-modern">
                  <tr>
                    {isColumnVisible('accessionNo') && (
                      <th>
                        <button
                          onClick={() => handleSort('accessionNo')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Accession No.</span>
                          {getSortIcon('accessionNo')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('title') && (
                      <th>
                        <button
                          onClick={() => handleSort('title')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Title</span>
                          {getSortIcon('title')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('author') && (
                      <th>
                        <button
                          onClick={() => handleSort('author')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Author</span>
                          {getSortIcon('author')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('category') && (
                      <th>
                        <button
                          onClick={() => handleSort('category')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Category</span>
                          {getSortIcon('category')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('language') && (
                      <th>
                        <button
                          onClick={() => handleSort('language')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Language</span>
                          {getSortIcon('language')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('publisher') && (
                      <th>
                        <button
                          onClick={() => handleSort('publisher')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Publisher</span>
                          {getSortIcon('publisher')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('year') && (
                      <th>
                        <button
                          onClick={() => handleSort('publishedYear')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Year</span>
                          {getSortIcon('publishedYear')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('copies') && <th>Copies</th>}
                    {isColumnVisible('price') && (
                      <th>
                        <button
                          onClick={() => handleSort('price')}
                          className="flex items-center space-x-1 hover:text-primary-600 transition-colors"
                        >
                          <span>Price</span>
                          {getSortIcon('price')}
                        </button>
                      </th>
                    )}
                    {isColumnVisible('status') && <th>Status</th>}
                    {isColumnVisible('location') && <th>Location</th>}
                    {isColumnVisible('actions') && <th>Actions</th>}
                  </tr>
                </thead>
                <tbody className="table-body-modern">
                  {books.map((book) => (
                    <tr
                      key={book.id}
                      className="table-row-hover"
                      onClick={() => handleRowClick(book.id)}
                    >
                      {isColumnVisible('accessionNo') && (
                        <td className="font-medium">{book.accessionNo}</td>
                      )}
                      {isColumnVisible('title') && (
                        <td>
                          <div>
                            <p className="font-medium text-gray-900">{book.title}</p>
                            {book.titleNepali && (
                              <p className="text-sm text-gray-500 text-nepali">{book.titleNepali}</p>
                            )}
                          </div>
                        </td>
                      )}
                      {isColumnVisible('author') && (
                        <td>
                          <div>
                            <p className="text-gray-900">{book.author.name}</p>
                            {book.author.nameNepali && (
                              <p className="text-sm text-gray-500 text-nepali">{book.author.nameNepali}</p>
                            )}
                          </div>
                        </td>
                      )}
                      {isColumnVisible('category') && (
                        <td>
                          <div>
                            <p className="text-gray-900">{book.category.name}</p>
                            {book.category.nameNepali && (
                              <p className="text-sm text-gray-500 text-nepali">{book.category.nameNepali}</p>
                            )}
                          </div>
                        </td>
                      )}
                      {isColumnVisible('language') && (
                        <td>
                          <div>
                            <p className="text-gray-900">{book.language.name}</p>
                            {book.language.nameNepali && (
                              <p className="text-sm text-gray-500 text-nepali">{book.language.nameNepali}</p>
                            )}
                          </div>
                        </td>
                      )}
                      {isColumnVisible('publisher') && (
                        <td>
                          {book.publisher_rel ? (
                            <div>
                              <p className="text-gray-900">{book.publisher_rel.name}</p>
                              {book.publisher_rel.nameNepali && (
                                <p className="text-sm text-gray-500 text-nepali">{book.publisher_rel.nameNepali}</p>
                              )}
                              {book.publisher_rel.country && (
                                <p className="text-xs text-gray-400">{book.publisher_rel.country}</p>
                              )}
                            </div>
                          ) : book.publisher ? (
                            <span className="text-gray-900">{book.publisher}</span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      )}
                      {isColumnVisible('year') && (
                        <td>{book.publishedYear}</td>
                      )}
                      {isColumnVisible('copies') && (
                        <td>
                          {book.copyInfo ? (
                            <div className="text-sm">
                              <div className="font-medium text-gray-900">
                                {book.copyInfo.totalCopies} total
                              </div>
                              <div className="text-xs text-gray-500">
                                {book.copyInfo.availableCopies} available
                                {book.copyInfo.borrowedCopies > 0 && (
                                  <span className="text-orange-600">
                                    , {book.copyInfo.borrowedCopies} borrowed
                                  </span>
                                )}
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm font-medium text-gray-900">
                              {book.bookNo || '1'}
                            </span>
                          )}
                        </td>
                      )}
                      {isColumnVisible('price') && (
                        <td>
                          {book.price ? (
                            <div>
                              <p className="text-gray-900">Rs. {book.price.toLocaleString()}</p>
                              {book.originalPrice && book.originalCurrency && book.originalCurrency !== 'NPR' && (
                                <p className="text-xs text-gray-500">
                                  {book.originalPrice} {book.originalCurrency}
                                </p>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      )}
                      {isColumnVisible('status') && (
                        <td>
                          {book.isAvailable ? (
                            <span className="badge badge-success">Available</span>
                          ) : (
                            <div>
                              <span className="badge badge-warning">Borrowed</span>
                              {book.borrowings.length > 0 && (
                                <p className="text-xs text-gray-500 mt-1">
                                  by {book.borrowings[0].member.name}
                                </p>
                              )}
                            </div>
                          )}
                        </td>
                      )}
                      {isColumnVisible('location') && (
                        <td>
                          {book.location ? (
                            <span className="text-sm text-gray-600">
                              {book.location.shelf}{book.location.row ? `-${book.location.row}` : ''}
                            </span>
                          ) : book.shelf && book.row ? (
                            <span className="text-sm text-gray-600">
                              {book.shelf}-{book.row}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-400">-</span>
                          )}
                        </td>
                      )}
                      {isColumnVisible('actions') && (
                        <td>
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={(e) => handleDownloadPDF(book, e)}
                              className="table-action-btn-view"
                              title="Download PDF"
                            >
                              <DocumentIcon className="w-4 h-4" />
                            </button>
                            <PermissionGuard permission="canManageBooks">
                              <Link
                                to={`/books/${book.id}/edit`}
                                className="table-action-btn-edit"
                                title="Edit Book"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <PencilIcon className="w-4 h-4" />
                              </Link>
                            </PermissionGuard>
                            <PermissionGuard permission="canManageBooks">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleDeleteClick(book.id, book.title)
                                }}
                                className="table-action-btn-delete"
                                title="Delete Book"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </PermissionGuard>
                          </div>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <DocumentArrowDownIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No books found</h3>
              <p className="text-gray-500 mb-4">Get started by adding your first book to the library.</p>
              <PermissionGuard permission="canManageBooks">
                <Link to="/books/new" className="btn btn-primary btn-md">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add First Book
                </Link>
              </PermissionGuard>
            </div>
          )}
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        pagination={pagination}
        onPageChange={handlePageChange}
        itemName="books"
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog({ isOpen: false, bookId: '', bookTitle: '' })}
        onConfirm={handleDeleteConfirm}
        title="Delete Book"
        message={`Are you sure you want to delete "${deleteDialog.bookTitle}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
    </div>
  )
}
