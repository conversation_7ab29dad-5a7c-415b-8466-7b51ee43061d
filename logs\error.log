
{"timestamp":"2025-06-01T08:08:22.975Z","level":"ERROR","message":"The \"path\" argument must be of type string. Received undefined","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"path\" argument must be of type string. Received undefined\n    at Object.join (node:path:478:7)\n    at Object.<anonymous> (D:\\library\\server\\index.js:108:42)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:23:07.985Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:223:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T08:23:07.976Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/PublisherDetail.tsx:601:105\n    at Array.map (<anonymous>)\n    at PublisherDetail (http://localhost:3000/src/pages/PublisherDetail.tsx:568:98)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=fe966b6b:19169:15)"},"errorInfo":{"componentStack":"\n    at PublisherDetail (http://localhost:3000/src/pages/PublisherDetail.tsx:37:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=fe966b6b:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=fe966b6b:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:18:5)\n    at App (http://localhost:3000/src/App.tsx:153:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=fe966b6b:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=fe966b6b:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:18:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/publishers/cmbde8crb001bvyrovj08n5w1","userId":null,"errorId":"error_1748766187974_fld1j2919"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:40:37.423Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:393:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:41:07.212Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:393:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:54:36.922Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:401:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:20:37.532Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T08:21:48.841Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T15:27:15.034Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'page')","stack":"Error: Cannot read properties of undefined (reading 'page')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T15:27:15.005Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'page')","stack":"TypeError: Cannot read properties of undefined (reading 'page')\n    at Languages (http://localhost:3000/src/pages/Languages.tsx?t=1748791582489:67:18)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at Languages (http://localhost:3000/src/pages/Languages.tsx?t=1748791582489:44:20)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:18:5)\n    at App (http://localhost:3000/src/App.tsx:153:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:18:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/languages","userId":null,"errorId":"error_1748791635002_ywaog921q"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T15:46:25.514Z","level":"ERROR","message":"fetchBooks is not defined","stack":"Error: fetchBooks is not defined\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T15:46:25.488Z","error":{"name":"ReferenceError","message":"fetchBooks is not defined","stack":"ReferenceError: fetchBooks is not defined\n    at http://localhost:3000/src/pages/LanguageDetail.tsx?t=1748792784379:54:7\n    at commitHookEffectListMount (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:16963:34)\n    at commitPassiveMountOnFiber (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18206:19)\n    at commitPassiveMountEffects_complete (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18179:17)\n    at commitPassiveMountEffects_begin (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18169:15)\n    at commitPassiveMountEffects (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18159:11)\n    at flushPassiveEffectsImpl (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19543:11)\n    at flushPassiveEffects (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19500:22)\n    at http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19381:17\n    at workLoop (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:197:42)"},"errorInfo":{"componentStack":"\n    at LanguageDetail (http://localhost:3000/src/pages/LanguageDetail.tsx?t=1748792784379:33:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/languages/cmbde8chq000xvyro94n7gcrp","userId":null,"errorId":"error_1748792785485_e84lef277"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T15:46:37.446Z","level":"ERROR","message":"Route not found: GET /api/languages/cmbde8chq000xvyro94n7gcrp/books?page=1&limit=10","stack":"NotFoundError: Route not found: GET /api/languages/cmbde8chq000xvyro94n7gcrp/books?page=1&limit=10\n    at notFoundHandler (D:\\library\\server\\middleware\\errorMiddleware.js:27:17)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\library\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\library\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\library\\node_modules\\express\\lib\\router\\index.js:47:12)","context":{"middleware":"notFoundHandler","category":"UNKNOWN","url":"/api/languages/cmbde8chq000xvyro94n7gcrp/books?page=1&limit=10","method":"GET","userId":null,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","ip":"::1","timestamp":"2025-06-01T15:46:37.446Z","nodeVersion":"v22.16.0","platform":"win32"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T15:54:39.235Z","level":"ERROR","message":"Route not found: GET /api/categories/cmbde8di2002ivyror2rxkhrd/books?page=1&limit=10","stack":"NotFoundError: Route not found: GET /api/categories/cmbde8di2002ivyror2rxkhrd/books?page=1&limit=10\n    at notFoundHandler (D:\\library\\server\\middleware\\errorMiddleware.js:27:17)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\library\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\library\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\library\\node_modules\\express\\lib\\router\\index.js:47:12)","context":{"middleware":"notFoundHandler","category":"UNKNOWN","url":"/api/categories/cmbde8di2002ivyror2rxkhrd/books?page=1&limit=10","method":"GET","userId":null,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","ip":"::1","timestamp":"2025-06-01T15:54:39.235Z","nodeVersion":"v22.16.0","platform":"win32"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:14:04.833Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'length')","stack":"Error: Cannot read properties of undefined (reading 'length')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:14:04.724Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'length')","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:232:36)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:33:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/authors/cmbduvlbm000gvym8rnmju5v1","userId":null,"errorId":"error_1748794444718_kg0v3rj45"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:15:03.581Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'length')","stack":"Error: Cannot read properties of undefined (reading 'length')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:15:03.509Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'length')","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:232:36)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:33:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/authors/cmbduvlor002vvym8mcl5fisq","userId":null,"errorId":"error_1748794503504_ym2692rkz"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:18:02.191Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'length')","stack":"Error: Cannot read properties of undefined (reading 'length')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:18:02.124Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'length')","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:232:36)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at AuthorDetail (http://localhost:3000/src/pages/AuthorDetail.tsx:33:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/authors/cmbduvlfg0010vym8c1ivqqr3","userId":null,"errorId":"error_1748794682119_65cqne5j3"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:22:49.474Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:22:49.413Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:504:99)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:34:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)\n    at App (http://localhost:3000/src/App.tsx?t=1748794951032:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm2h005kvym89w9y498x","userId":null,"errorId":"error_1748794969407_owebfjxhf"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:25:34.407Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:25:34.391Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795128346:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795128346:504:99)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795128346:34:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)\n    at App (http://localhost:3000/src/App.tsx?t=1748795008271:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm2l005lvym84eyrgxys","userId":null,"errorId":"error_1748795134389_1n0ikuq7k"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:29:14.140Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:29:14.128Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795153895:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795153895:504:100)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx?t=1748795153895:34:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)\n    at App (http://localhost:3000/src/App.tsx?t=1748795224683:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx?t=1748794927929:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm2r005mvym8z3ebaxkc","userId":null,"errorId":"error_1748795354126_m9buq365r"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:30:27.951Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:30:27.916Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:504:100)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:34:18)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm1x005gvym85h72669q","userId":null,"errorId":"error_1748795427912_2xwt8mx2n"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:30:40.604Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:30:40.589Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:504:100)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:34:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm2r005mvym8z3ebaxkc","userId":null,"errorId":"error_1748795440585_r9mla6dky"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T16:31:00.153Z","level":"ERROR","message":"Cannot read properties of undefined (reading 'name')","stack":"Error: Cannot read properties of undefined (reading 'name')\n    at D:\\library\\server\\index.js:243:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-01T16:31:00.146Z","error":{"name":"TypeError","message":"Cannot read properties of undefined (reading 'name')","stack":"TypeError: Cannot read properties of undefined (reading 'name')\n    at http://localhost:3000/src/pages/BookSeriesDetail.tsx:537:105\n    at Array.map (<anonymous>)\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:504:100)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)"},"errorInfo":{"componentStack":"\n    at BookSeriesDetail (http://localhost:3000/src/pages/BookSeriesDetail.tsx:34:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:31)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"http://localhost:3000/book-series/cmbduvm2l005lvym84eyrgxys","userId":null,"errorId":"error_1748795460143_qup9ng6kt"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T17:10:30.871Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T17:13:03.589Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T17:16:10.699Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-01T17:22:14.574Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:415:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T00:52:13.508Z","level":"ERROR","message":"paginatedLanguages is not defined","stack":"Error: paginatedLanguages is not defined\n    at D:\\library\\server\\index.js:244:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-02T00:52:13.488Z","error":{"name":"ReferenceError","message":"paginatedLanguages is not defined","stack":"ReferenceError: paginatedLanguages is not defined\n    at Languages (http://localhost:3000/src/pages/Languages.tsx?t=1748825532261:246:16)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performSyncWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18932:28)"},"errorInfo":{"componentStack":"\n    at Languages (http://localhost:3000/src/pages/Languages.tsx?t=1748825532261:44:20)\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:58)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/languages","userId":null,"errorId":"error_1748825533485_utta98vx6"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T01:16:30.523Z","level":"ERROR","message":"fetchBooks is not defined","stack":"Error: fetchBooks is not defined\n    at D:\\library\\server\\index.js:244:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-02T01:16:30.478Z","error":{"name":"ReferenceError","message":"fetchBooks is not defined","stack":"ReferenceError: fetchBooks is not defined\n    at http://localhost:3000/src/pages/PublisherDetail.tsx?t=1748826990084:59:7\n    at commitHookEffectListMount (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:16963:34)\n    at commitPassiveMountOnFiber (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18206:19)\n    at commitPassiveMountEffects_complete (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18179:17)\n    at commitPassiveMountEffects_begin (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18169:15)\n    at commitPassiveMountEffects (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18159:11)\n    at flushPassiveEffectsImpl (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19543:11)\n    at flushPassiveEffects (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19500:22)\n    at commitRootImpl (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19469:13)\n    at commitRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19330:13)"},"errorInfo":{"componentStack":"\n    at PublisherDetail (http://localhost:3000/src/pages/PublisherDetail.tsx?t=1748826990084:37:18)\n    at http://localhost:3000/src/hooks/usePermissions.tsx:131:25\n    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4088:5)\n    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4558:5)\n    at div\n    at div\n    at main\n    at div\n    at div\n    at Layout (http://localhost:3000/src/components/Layout.tsx:84:34)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)\n    at App (http://localhost:3000/src/App.tsx:155:58)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"http://localhost:3000/publishers/cmbee8buy000wvy1keh1j9t0u","userId":null,"errorId":"error_1748826990474_0dg42100h"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T01:16:44.472Z","level":"ERROR","message":"Route not found: GET /api/publishers/cmbee8buy000wvy1keh1j9t0u/books?page=1&limit=10&sortBy=title&sortOrder=asc","stack":"NotFoundError: Route not found: GET /api/publishers/cmbee8buy000wvy1keh1j9t0u/books?page=1&limit=10&sortBy=title&sortOrder=asc\n    at notFoundHandler (D:\\library\\server\\middleware\\errorMiddleware.js:27:17)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\library\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\library\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\library\\node_modules\\express\\lib\\router\\index.js:47:12)","context":{"middleware":"notFoundHandler","category":"UNKNOWN","url":"/api/publishers/cmbee8buy000wvy1keh1j9t0u/books?page=1&limit=10&sortBy=title&sortOrder=asc","method":"GET","userId":null,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-06-02T01:16:44.472Z","nodeVersion":"v22.16.0","platform":"win32"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T01:30:24.571Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:416:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T01:32:29.790Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:416:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T01:43:02.788Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\library\\server\\index.js:416:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T02:19:24.442Z","level":"ERROR","message":"BugAntIcon is not defined","stack":"Error: BugAntIcon is not defined\n    at D:\\library\\server\\index.js:244:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-02T02:19:24.427Z","error":{"name":"ReferenceError","message":"BugAntIcon is not defined","stack":"ReferenceError: BugAntIcon is not defined\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830763419:185:46)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at updateFunctionComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14630:28)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15972:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performSyncWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18932:28)"},"errorInfo":{"componentStack":"\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830763419:26:43)\n    at App (http://localhost:3000/src/App.tsx:155:58)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/user-management","userId":null,"errorId":"error_1748830764424_qv07z14vp"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T02:19:33.707Z","level":"ERROR","message":"BugAntIcon is not defined","stack":"Error: BugAntIcon is not defined\n    at D:\\library\\server\\index.js:244:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-02T02:19:33.657Z","error":{"name":"ReferenceError","message":"BugAntIcon is not defined","stack":"ReferenceError: BugAntIcon is not defined\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830763419:185:46)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at mountIndeterminateComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14974:21)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15962:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830763419:26:43)\n    at App (http://localhost:3000/src/App.tsx?t=1748830763419:155:58)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/dashboard","userId":null,"errorId":"error_1748830773647_zv5zm3oxk"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-02T02:19:41.127Z","level":"ERROR","message":"handleDebug is not defined","stack":"Error: handleDebug is not defined\n    at D:\\library\\server\\index.js:244:39\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\library\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\library\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\library\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\library\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\library\\node_modules\\body-parser\\lib\\types\\urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (D:\\library\\node_modules\\express\\lib\\router\\layer.js:95:5)","context":{"type":"frontend","timestamp":"2025-06-02T02:19:41.066Z","error":{"name":"ReferenceError","message":"handleDebug is not defined","stack":"ReferenceError: handleDebug is not defined\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830776573:146:22)\n    at renderWithHooks (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:11596:26)\n    at mountIndeterminateComponent (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:14974:21)\n    at beginWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:15962:22)\n    at beginWork$1 (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19806:22)\n    at performUnitOfWork (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19251:20)\n    at workLoopSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19190:13)\n    at renderRootSync (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:19169:15)\n    at recoverFromConcurrentError (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18786:28)\n    at performConcurrentWorkOnRoot (http://localhost:3000/node_modules/.vite/deps/chunk-OY5C42Z6.js?v=6152a2e7:18734:30)"},"errorInfo":{"componentStack":"\n    at Login (http://localhost:3000/src/pages/Login.tsx?t=1748830776573:26:43)\n    at App (http://localhost:3000/src/App.tsx?t=1748830776573:155:58)\n    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:4501:15)\n    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=6152a2e7:5247:5)\n    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.tsx:22:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) maithili-vikash-kosh-library/1.0.0 Chrome/120.0.6099.291 Electron/28.3.3 Safari/537.36","url":"http://localhost:3000/dashboard","userId":null,"errorId":"error_1748830781056_l7owf9hhz"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-03T05:51:22.974Z","level":"ERROR","message":"requireManageBooks is not defined","stack":"ReferenceError: requireManageBooks is not defined\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\routes\\authors.js:313:36)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\index.js:168:22)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-03T05:51:44.709Z","level":"ERROR","message":"requireManageBooks is not defined","stack":"ReferenceError: requireManageBooks is not defined\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\routes\\authors.js:313:36)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\index.js:168:22)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
{"timestamp":"2025-06-03T06:41:17.495Z","level":"ERROR","message":"listen EADDRINUSE: address already in use :::3002","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\libraryFinal\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\index.js:416:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","context":{"type":"uncaughtException"},"process":"server","version":"1.0.0"}
